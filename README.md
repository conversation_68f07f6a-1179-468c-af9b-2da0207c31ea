# MCP 天气查询服务

基于 Spring Boot 3.5.4 和 JDK 21 开发的微服务通信协议（MCP）天气查询服务，集成 OpenMeteo API 提供天气查询功能。

## 功能特性

- ✅ 支持根据城市名称查询天气
- ✅ 支持根据经纬度查询天气
- ✅ 提供当前天气和未来预报查询
- ✅ 集成 OpenMeteo API 和地理编码服务
- ✅ 实现重试机制和错误处理
- ✅ 提供 RESTful API 接口
- ✅ 包含完整的单元测试和集成测试
- ✅ 支持健康检查和监控

## 技术栈

- **Java**: JDK 21
- **框架**: Spring Boot 3.5.4
- **Web**: Spring WebFlux (响应式编程)
- **HTTP 客户端**: WebClient
- **重试机制**: Spring Retry
- **测试**: JUnit 5, Mockito, MockWebServer
- **构建工具**: Maven
- **外部 API**: OpenMeteo Weather API

## 项目结构

```
src/main/java/com/mira/mcp/demo/
├── McpDemoApplication.java                 # 主应用类
└── weather/
    ├── controller/
    │   └── WeatherController.java          # REST 控制器
    ├── service/
    │   ├── WeatherService.java             # 服务接口
    │   └── impl/
    │       └── WeatherServiceImpl.java     # 服务实现
    ├── client/
    │   ├── OpenMeteoClient.java            # OpenMeteo API 客户端接口
    │   ├── GeocodingClient.java            # 地理编码客户端接口
    │   └── impl/
    │       ├── OpenMeteoClientImpl.java    # OpenMeteo API 客户端实现
    │       └── GeocodingClientImpl.java    # 地理编码客户端实现
    ├── model/
    │   ├── dto/                            # 数据传输对象
    │   │   ├── WeatherRequest.java
    │   │   ├── WeatherResponse.java
    │   │   └── LocationInfo.java
    │   └── external/                       # 外部 API 响应模型
    │       ├── OpenMeteoResponse.java
    │       └── GeocodingResponse.java
    ├── config/
    │   ├── WeatherConfig.java              # 天气服务配置
    │   └── RetryConfig.java                # 重试配置
    ├── exception/
    │   ├── WeatherServiceException.java    # 自定义异常
    │   └── GlobalExceptionHandler.java     # 全局异常处理
    └── util/
        └── WeatherCodeUtil.java            # 天气代码工具类
```

## 快速开始

### 环境要求

- JDK 21+
- Maven 3.6+

### 启动应用

1. 克隆项目
```bash
git clone <repository-url>
cd mcp-demo
```

2. 编译项目
```bash
mvn clean compile
```

3. 运行测试
```bash
mvn test
```

4. 启动应用
```bash
mvn spring-boot:run
```

应用将在 `http://localhost:8080` 启动。

### 配置说明

主要配置项在 `application.properties` 中：

```properties
# 服务端口
server.port=8080

# OpenMeteo API 配置
weather.openmeteo.base-url=https://api.open-meteo.com/v1
weather.openmeteo.timeout=10000
weather.openmeteo.retry.max-attempts=3
weather.openmeteo.retry.delay=1000

# 监控配置
management.endpoints.web.exposure.include=health,info,metrics
management.endpoint.health.show-details=when-authorized

# 日志配置
logging.level.com.mira.mcp.demo.weather=DEBUG
```

## API 接口文档

### 1. 根据城市名称获取当前天气

**GET** `/api/v1/weather/current/city`

**参数:**
- `city` (string, required): 城市名称

**示例:**
```bash
curl "http://localhost:8080/api/v1/weather/current/city?city=北京"
```

### 2. 根据经纬度获取当前天气

**GET** `/api/v1/weather/current/coordinates`

**参数:**
- `latitude` (double, required): 纬度 (-90 到 90)
- `longitude` (double, required): 经度 (-180 到 180)

**示例:**
```bash
curl "http://localhost:8080/api/v1/weather/current/coordinates?latitude=39.9042&longitude=116.4074"
```

### 3. 根据城市名称获取天气预报

**GET** `/api/v1/weather/forecast/city`

**参数:**
- `city` (string, required): 城市名称
- `days` (integer, optional): 预报天数，默认7天，最大14天

**示例:**
```bash
curl "http://localhost:8080/api/v1/weather/forecast/city?city=上海&days=5"
```

### 4. 根据经纬度获取天气预报

**GET** `/api/v1/weather/forecast/coordinates`

**参数:**
- `latitude` (double, required): 纬度
- `longitude` (double, required): 经度
- `days` (integer, optional): 预报天数，默认7天，最大14天

**示例:**
```bash
curl "http://localhost:8080/api/v1/weather/forecast/coordinates?latitude=31.2304&longitude=121.4737&days=3"
```

### 5. 通用天气查询接口

**POST** `/api/v1/weather/query`

**请求体:**
```json
{
  "city": "深圳",
  "includeForecast": true,
  "forecastDays": 7
}
```

或者使用经纬度：
```json
{
  "latitude": 22.5431,
  "longitude": 114.0579,
  "includeForecast": false
}
```

### 6. 健康检查

**GET** `/api/v1/weather/health`

**响应:**
```
Weather service is running
```

## 响应格式

### 成功响应示例

```json
{
  "location": {
    "name": "北京",
    "latitude": 39.9042,
    "longitude": 116.4074,
    "country": "中国",
    "timezone": "Asia/Shanghai"
  },
  "current": {
    "temperature": 20.5,
    "humidity": 65.0,
    "windSpeed": 5.2,
    "windDirection": 180,
    "pressure": 1013.25,
    "weatherDescription": "主要晴朗",
    "weatherCode": 1,
    "time": "2024-01-01 12:00:00"
  },
  "forecast": [
    {
      "date": "2024-01-02",
      "maxTemperature": 25.0,
      "minTemperature": 15.0,
      "weatherDescription": "晴朗",
      "weatherCode": 0,
      "precipitation": 0.0,
      "windSpeed": 8.0
    }
  ],
  "queryTime": "2024-01-01 12:00:00"
}
```

### 错误响应示例

```json
{
  "error": "CITY_NOT_FOUND",
  "message": "未找到指定城市",
  "timestamp": "2024-01-01 12:00:00"
}
```

## 监控和健康检查

应用提供以下监控端点：

- **健康检查**: `GET /actuator/health`
- **应用信息**: `GET /actuator/info`
- **指标信息**: `GET /actuator/metrics`

## 测试

### 运行单元测试
```bash
mvn test
```

### 运行集成测试
```bash
mvn test -Dtest=*IntegrationTest
```

### 测试覆盖率
```bash
mvn jacoco:report
```

## 部署

### 构建 JAR 包
```bash
mvn clean package
```

### 运行 JAR 包
```bash
java -jar target/mcp-demo-0.0.1-SNAPSHOT.jar
```

### Docker 部署
```dockerfile
FROM openjdk:21-jre-slim
COPY target/mcp-demo-0.0.1-SNAPSHOT.jar app.jar
EXPOSE 8080
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

## 依赖信息

主要依赖包括：

- Spring Boot Starter Web
- Spring Boot Starter WebFlux
- Spring Boot Starter Actuator
- Spring Boot Starter Validation
- Spring Retry
- Spring Boot Starter Test
- MockWebServer (测试)

完整依赖列表请查看 `pom.xml` 文件。

## 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

如有问题或建议，请通过以下方式联系：

- 项目 Issues: [GitHub Issues](https://github.com/your-repo/issues)
- 邮箱: <EMAIL>
