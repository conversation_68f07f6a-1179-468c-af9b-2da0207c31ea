spring.application.name=mcp-demo
server.port=8080

# Weather Service Configuration
weather.openmeteo.base-url=https://api.open-meteo.com/v1
weather.openmeteo.timeout=10000
weather.openmeteo.retry.max-attempts=3
weather.openmeteo.retry.delay=1000

# Actuator Configuration
management.endpoints.web.exposure.include=health,info,metrics
management.endpoint.health.show-details=when-authorized

# Logging Configuration
logging.level.com.mira.mcp.demo.weather=DEBUG
logging.level.org.springframework.web.reactive.function.client=DEBUG
