package com.mira.mcp.demo.weather.client;

import com.mira.mcp.demo.weather.model.external.OpenMeteoResponse;
import reactor.core.publisher.Mono;

/**
 * OpenMeteo API 客户端接口
 */
public interface OpenMeteoClient {
    
    /**
     * 根据经纬度获取当前天气
     * 
     * @param latitude 纬度
     * @param longitude 经度
     * @return 天气响应
     */
    Mono<OpenMeteoResponse> getCurrentWeather(double latitude, double longitude);
    
    /**
     * 根据经纬度获取天气预报
     * 
     * @param latitude 纬度
     * @param longitude 经度
     * @param days 预报天数
     * @return 天气响应
     */
    Mono<OpenMeteoResponse> getWeatherForecast(double latitude, double longitude, int days);
    
    /**
     * 根据经纬度获取当前天气和预报
     * 
     * @param latitude 纬度
     * @param longitude 经度
     * @param days 预报天数
     * @return 天气响应
     */
    Mono<OpenMeteoResponse> getCurrentWeatherAndForecast(double latitude, double longitude, int days);
}
