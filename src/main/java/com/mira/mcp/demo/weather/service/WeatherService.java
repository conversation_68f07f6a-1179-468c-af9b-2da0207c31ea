package com.mira.mcp.demo.weather.service;

import com.mira.mcp.demo.weather.model.dto.WeatherRequest;
import com.mira.mcp.demo.weather.model.dto.WeatherResponse;
import reactor.core.publisher.Mono;

/**
 * 天气服务接口
 */
public interface WeatherService {
    
    /**
     * 获取天气信息
     * 
     * @param request 天气查询请求
     * @return 天气响应
     */
    Mono<WeatherResponse> getWeather(WeatherRequest request);
    
    /**
     * 根据城市名称获取当前天气
     * 
     * @param cityName 城市名称
     * @return 天气响应
     */
    Mono<WeatherResponse> getCurrentWeatherByCity(String cityName);
    
    /**
     * 根据经纬度获取当前天气
     * 
     * @param latitude 纬度
     * @param longitude 经度
     * @return 天气响应
     */
    Mono<WeatherResponse> getCurrentWeatherByCoordinates(double latitude, double longitude);
    
    /**
     * 根据城市名称获取天气预报
     * 
     * @param cityName 城市名称
     * @param days 预报天数
     * @return 天气响应
     */
    Mono<WeatherResponse> getWeatherForecastByCity(String cityName, int days);
    
    /**
     * 根据经纬度获取天气预报
     * 
     * @param latitude 纬度
     * @param longitude 经度
     * @param days 预报天数
     * @return 天气响应
     */
    Mono<WeatherResponse> getWeatherForecastByCoordinates(double latitude, double longitude, int days);
}
