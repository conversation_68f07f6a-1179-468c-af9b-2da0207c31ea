package com.mira.mcp.demo.weather.client.impl;

import com.mira.mcp.demo.weather.client.GeocodingClient;
import com.mira.mcp.demo.weather.exception.WeatherServiceException;
import com.mira.mcp.demo.weather.model.dto.LocationInfo;
import com.mira.mcp.demo.weather.model.external.GeocodingResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientException;
import reactor.core.publisher.Mono;

/**
 * 地理编码客户端实现
 */
@Component
public class GeocodingClientImpl implements GeocodingClient {
    
    private static final Logger logger = LoggerFactory.getLogger(GeocodingClientImpl.class);
    
    private final WebClient webClient;
    
    public GeocodingClientImpl(WebClient weatherWebClient) {
        this.webClient = weatherWebClient;
    }
    
    @Override
    @Retryable(
        retryFor = {WebClientException.class},
        maxAttempts = 3,
        backoff = @Backoff(delay = 1000)
    )
    public Mono<LocationInfo> getLocationByCity(String cityName) {
        logger.debug("Geocoding city: {}", cityName);
        
        return webClient.get()
                .uri(uriBuilder -> uriBuilder
                        .path("/search")
                        .queryParam("name", cityName)
                        .queryParam("count", 1)
                        .queryParam("language", "zh")
                        .queryParam("format", "json")
                        .build())
                .retrieve()
                .bodyToMono(GeocodingResponse.class)
                .map(this::convertToLocationInfo)
                .doOnSuccess(location -> logger.debug("Successfully geocoded city: {} -> {}", cityName, location))
                .doOnError(error -> logger.error("Failed to geocode city {}: {}", cityName, error.getMessage()))
                .onErrorMap(WebClientException.class, ex -> 
                    new WeatherServiceException("GEOCODING_ERROR", "城市地理编码失败: " + cityName, ex));
    }
    
    private LocationInfo convertToLocationInfo(GeocodingResponse response) {
        if (response.getResults() == null || response.getResults().isEmpty()) {
            throw new WeatherServiceException("CITY_NOT_FOUND", "未找到指定城市");
        }
        
        GeocodingResponse.Result result = response.getResults().get(0);
        LocationInfo locationInfo = new LocationInfo();
        locationInfo.setName(result.getName());
        locationInfo.setLatitude(result.getLatitude());
        locationInfo.setLongitude(result.getLongitude());
        locationInfo.setCountry(result.getCountry());
        locationInfo.setTimezone(result.getTimezone());
        
        return locationInfo;
    }
}
