package com.mira.mcp.demo.weather.util;

import java.util.HashMap;
import java.util.Map;

/**
 * 天气代码工具类
 * 根据 WMO Weather interpretation codes 转换为中文描述
 */
public class WeatherCodeUtil {
    
    private static final Map<Integer, String> WEATHER_CODE_MAP = new HashMap<>();
    
    static {
        WEATHER_CODE_MAP.put(0, "晴朗");
        WEATHER_CODE_MAP.put(1, "主要晴朗");
        WEATHER_CODE_MAP.put(2, "部分多云");
        WEATHER_CODE_MAP.put(3, "阴天");
        WEATHER_CODE_MAP.put(45, "雾");
        WEATHER_CODE_MAP.put(48, "沉积雾霜");
        WEATHER_CODE_MAP.put(51, "小毛毛雨");
        WEATHER_CODE_MAP.put(53, "中等毛毛雨");
        WEATHER_CODE_MAP.put(55, "密集毛毛雨");
        WEATHER_CODE_MAP.put(56, "轻微冻毛毛雨");
        WEATHER_CODE_MAP.put(57, "密集冻毛毛雨");
        WEATHER_CODE_MAP.put(61, "小雨");
        WEATHER_CODE_MAP.put(63, "中雨");
        WEATHER_CODE_MAP.put(65, "大雨");
        WEATHER_CODE_MAP.put(66, "轻微冻雨");
        WEATHER_CODE_MAP.put(67, "大冻雨");
        WEATHER_CODE_MAP.put(71, "小雪");
        WEATHER_CODE_MAP.put(73, "中雪");
        WEATHER_CODE_MAP.put(75, "大雪");
        WEATHER_CODE_MAP.put(77, "雪粒");
        WEATHER_CODE_MAP.put(80, "小阵雨");
        WEATHER_CODE_MAP.put(81, "中等阵雨");
        WEATHER_CODE_MAP.put(82, "强烈阵雨");
        WEATHER_CODE_MAP.put(85, "小阵雪");
        WEATHER_CODE_MAP.put(86, "大阵雪");
        WEATHER_CODE_MAP.put(95, "雷暴");
        WEATHER_CODE_MAP.put(96, "轻微冰雹雷暴");
        WEATHER_CODE_MAP.put(99, "强烈冰雹雷暴");
    }
    
    /**
     * 根据天气代码获取中文描述
     * 
     * @param weatherCode 天气代码
     * @return 中文天气描述
     */
    public static String getWeatherDescription(Integer weatherCode) {
        if (weatherCode == null) {
            return "未知";
        }
        return WEATHER_CODE_MAP.getOrDefault(weatherCode, "未知天气");
    }
    
    /**
     * 判断是否为晴朗天气
     * 
     * @param weatherCode 天气代码
     * @return 是否晴朗
     */
    public static boolean isClearWeather(Integer weatherCode) {
        return weatherCode != null && (weatherCode == 0 || weatherCode == 1);
    }
    
    /**
     * 判断是否为雨天
     * 
     * @param weatherCode 天气代码
     * @return 是否雨天
     */
    public static boolean isRainyWeather(Integer weatherCode) {
        return weatherCode != null && 
               ((weatherCode >= 51 && weatherCode <= 67) || 
                (weatherCode >= 80 && weatherCode <= 82));
    }
    
    /**
     * 判断是否为雪天
     * 
     * @param weatherCode 天气代码
     * @return 是否雪天
     */
    public static boolean isSnowyWeather(Integer weatherCode) {
        return weatherCode != null && 
               ((weatherCode >= 71 && weatherCode <= 77) || 
                (weatherCode >= 85 && weatherCode <= 86));
    }
    
    /**
     * 判断是否为恶劣天气
     * 
     * @param weatherCode 天气代码
     * @return 是否恶劣天气
     */
    public static boolean isSevereWeather(Integer weatherCode) {
        return weatherCode != null && 
               (weatherCode >= 95 && weatherCode <= 99);
    }
}
