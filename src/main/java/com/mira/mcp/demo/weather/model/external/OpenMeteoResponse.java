package com.mira.mcp.demo.weather.model.external;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * OpenMeteo API 响应模型
 */
public class OpenMeteoResponse {
    
    private Double latitude;
    private Double longitude;
    
    @JsonProperty("generationtime_ms")
    private Double generationTimeMs;
    
    @JsonProperty("utc_offset_seconds")
    private Integer utcOffsetSeconds;
    
    private String timezone;
    
    @JsonProperty("timezone_abbreviation")
    private String timezoneAbbreviation;
    
    private Double elevation;
    
    @JsonProperty("current_units")
    private CurrentUnits currentUnits;
    
    private Current current;
    
    @JsonProperty("daily_units")
    private DailyUnits dailyUnits;
    
    private Daily daily;

    // Getters and Setters
    public Double getLatitude() {
        return latitude;
    }

    public void setLatitude(Double latitude) {
        this.latitude = latitude;
    }

    public Double getLongitude() {
        return longitude;
    }

    public void setLongitude(Double longitude) {
        this.longitude = longitude;
    }

    public Double getGenerationTimeMs() {
        return generationTimeMs;
    }

    public void setGenerationTimeMs(Double generationTimeMs) {
        this.generationTimeMs = generationTimeMs;
    }

    public Integer getUtcOffsetSeconds() {
        return utcOffsetSeconds;
    }

    public void setUtcOffsetSeconds(Integer utcOffsetSeconds) {
        this.utcOffsetSeconds = utcOffsetSeconds;
    }

    public String getTimezone() {
        return timezone;
    }

    public void setTimezone(String timezone) {
        this.timezone = timezone;
    }

    public String getTimezoneAbbreviation() {
        return timezoneAbbreviation;
    }

    public void setTimezoneAbbreviation(String timezoneAbbreviation) {
        this.timezoneAbbreviation = timezoneAbbreviation;
    }

    public Double getElevation() {
        return elevation;
    }

    public void setElevation(Double elevation) {
        this.elevation = elevation;
    }

    public CurrentUnits getCurrentUnits() {
        return currentUnits;
    }

    public void setCurrentUnits(CurrentUnits currentUnits) {
        this.currentUnits = currentUnits;
    }

    public Current getCurrent() {
        return current;
    }

    public void setCurrent(Current current) {
        this.current = current;
    }

    public DailyUnits getDailyUnits() {
        return dailyUnits;
    }

    public void setDailyUnits(DailyUnits dailyUnits) {
        this.dailyUnits = dailyUnits;
    }

    public Daily getDaily() {
        return daily;
    }

    public void setDaily(Daily daily) {
        this.daily = daily;
    }

    /**
     * 当前天气单位
     */
    public static class CurrentUnits {
        private String time;
        private String interval;
        
        @JsonProperty("temperature_2m")
        private String temperature2m;
        
        @JsonProperty("relative_humidity_2m")
        private String relativeHumidity2m;
        
        @JsonProperty("wind_speed_10m")
        private String windSpeed10m;
        
        @JsonProperty("wind_direction_10m")
        private String windDirection10m;
        
        @JsonProperty("surface_pressure")
        private String surfacePressure;
        
        @JsonProperty("weather_code")
        private String weatherCode;

        // Getters and Setters
        public String getTime() {
            return time;
        }

        public void setTime(String time) {
            this.time = time;
        }

        public String getInterval() {
            return interval;
        }

        public void setInterval(String interval) {
            this.interval = interval;
        }

        public String getTemperature2m() {
            return temperature2m;
        }

        public void setTemperature2m(String temperature2m) {
            this.temperature2m = temperature2m;
        }

        public String getRelativeHumidity2m() {
            return relativeHumidity2m;
        }

        public void setRelativeHumidity2m(String relativeHumidity2m) {
            this.relativeHumidity2m = relativeHumidity2m;
        }

        public String getWindSpeed10m() {
            return windSpeed10m;
        }

        public void setWindSpeed10m(String windSpeed10m) {
            this.windSpeed10m = windSpeed10m;
        }

        public String getWindDirection10m() {
            return windDirection10m;
        }

        public void setWindDirection10m(String windDirection10m) {
            this.windDirection10m = windDirection10m;
        }

        public String getSurfacePressure() {
            return surfacePressure;
        }

        public void setSurfacePressure(String surfacePressure) {
            this.surfacePressure = surfacePressure;
        }

        public String getWeatherCode() {
            return weatherCode;
        }

        public void setWeatherCode(String weatherCode) {
            this.weatherCode = weatherCode;
        }
    }

    /**
     * 当前天气数据
     */
    public static class Current {
        private String time;
        private Integer interval;
        
        @JsonProperty("temperature_2m")
        private Double temperature2m;
        
        @JsonProperty("relative_humidity_2m")
        private Double relativeHumidity2m;
        
        @JsonProperty("wind_speed_10m")
        private Double windSpeed10m;
        
        @JsonProperty("wind_direction_10m")
        private Integer windDirection10m;
        
        @JsonProperty("surface_pressure")
        private Double surfacePressure;
        
        @JsonProperty("weather_code")
        private Integer weatherCode;

        // Getters and Setters
        public String getTime() {
            return time;
        }

        public void setTime(String time) {
            this.time = time;
        }

        public Integer getInterval() {
            return interval;
        }

        public void setInterval(Integer interval) {
            this.interval = interval;
        }

        public Double getTemperature2m() {
            return temperature2m;
        }

        public void setTemperature2m(Double temperature2m) {
            this.temperature2m = temperature2m;
        }

        public Double getRelativeHumidity2m() {
            return relativeHumidity2m;
        }

        public void setRelativeHumidity2m(Double relativeHumidity2m) {
            this.relativeHumidity2m = relativeHumidity2m;
        }

        public Double getWindSpeed10m() {
            return windSpeed10m;
        }

        public void setWindSpeed10m(Double windSpeed10m) {
            this.windSpeed10m = windSpeed10m;
        }

        public Integer getWindDirection10m() {
            return windDirection10m;
        }

        public void setWindDirection10m(Integer windDirection10m) {
            this.windDirection10m = windDirection10m;
        }

        public Double getSurfacePressure() {
            return surfacePressure;
        }

        public void setSurfacePressure(Double surfacePressure) {
            this.surfacePressure = surfacePressure;
        }

        public Integer getWeatherCode() {
            return weatherCode;
        }

        public void setWeatherCode(Integer weatherCode) {
            this.weatherCode = weatherCode;
        }
    }

    /**
     * 每日天气单位
     */
    public static class DailyUnits {
        private String time;

        @JsonProperty("temperature_2m_max")
        private String temperature2mMax;

        @JsonProperty("temperature_2m_min")
        private String temperature2mMin;

        @JsonProperty("weather_code")
        private String weatherCode;

        @JsonProperty("precipitation_sum")
        private String precipitationSum;

        @JsonProperty("wind_speed_10m_max")
        private String windSpeed10mMax;

        // Getters and Setters
        public String getTime() {
            return time;
        }

        public void setTime(String time) {
            this.time = time;
        }

        public String getTemperature2mMax() {
            return temperature2mMax;
        }

        public void setTemperature2mMax(String temperature2mMax) {
            this.temperature2mMax = temperature2mMax;
        }

        public String getTemperature2mMin() {
            return temperature2mMin;
        }

        public void setTemperature2mMin(String temperature2mMin) {
            this.temperature2mMin = temperature2mMin;
        }

        public String getWeatherCode() {
            return weatherCode;
        }

        public void setWeatherCode(String weatherCode) {
            this.weatherCode = weatherCode;
        }

        public String getPrecipitationSum() {
            return precipitationSum;
        }

        public void setPrecipitationSum(String precipitationSum) {
            this.precipitationSum = precipitationSum;
        }

        public String getWindSpeed10mMax() {
            return windSpeed10mMax;
        }

        public void setWindSpeed10mMax(String windSpeed10mMax) {
            this.windSpeed10mMax = windSpeed10mMax;
        }
    }

    /**
     * 每日天气数据
     */
    public static class Daily {
        private List<String> time;

        @JsonProperty("temperature_2m_max")
        private List<Double> temperature2mMax;

        @JsonProperty("temperature_2m_min")
        private List<Double> temperature2mMin;

        @JsonProperty("weather_code")
        private List<Integer> weatherCode;

        @JsonProperty("precipitation_sum")
        private List<Double> precipitationSum;

        @JsonProperty("wind_speed_10m_max")
        private List<Double> windSpeed10mMax;

        // Getters and Setters
        public List<String> getTime() {
            return time;
        }

        public void setTime(List<String> time) {
            this.time = time;
        }

        public List<Double> getTemperature2mMax() {
            return temperature2mMax;
        }

        public void setTemperature2mMax(List<Double> temperature2mMax) {
            this.temperature2mMax = temperature2mMax;
        }

        public List<Double> getTemperature2mMin() {
            return temperature2mMin;
        }

        public void setTemperature2mMin(List<Double> temperature2mMin) {
            this.temperature2mMin = temperature2mMin;
        }

        public List<Integer> getWeatherCode() {
            return weatherCode;
        }

        public void setWeatherCode(List<Integer> weatherCode) {
            this.weatherCode = weatherCode;
        }

        public List<Double> getPrecipitationSum() {
            return precipitationSum;
        }

        public void setPrecipitationSum(List<Double> precipitationSum) {
            this.precipitationSum = precipitationSum;
        }

        public List<Double> getWindSpeed10mMax() {
            return windSpeed10mMax;
        }

        public void setWindSpeed10mMax(List<Double> windSpeed10mMax) {
            this.windSpeed10mMax = windSpeed10mMax;
        }
    }
}
