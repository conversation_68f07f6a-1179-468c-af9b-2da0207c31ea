package com.mira.mcp.demo.weather.model.dto;

import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 天气查询请求参数
 */
public class WeatherRequest {
    
    /**
     * 城市名称（可选，与经纬度二选一）
     */
    @Size(max = 100, message = "城市名称长度不能超过100个字符")
    private String city;
    
    /**
     * 纬度（可选，与城市名称二选一）
     */
    @DecimalMin(value = "-90.0", message = "纬度必须在-90到90之间")
    @DecimalMax(value = "90.0", message = "纬度必须在-90到90之间")
    private Double latitude;
    
    /**
     * 经度（可选，与城市名称二选一）
     */
    @DecimalMin(value = "-180.0", message = "经度必须在-180到180之间")
    @DecimalMax(value = "180.0", message = "经度必须在-180到180之间")
    private Double longitude;
    
    /**
     * 是否包含预报信息，默认false
     */
    private boolean includeForecast = false;
    
    /**
     * 预报天数，默认7天，最大14天
     */
    private int forecastDays = 7;

    public WeatherRequest() {}

    public WeatherRequest(String city) {
        this.city = city;
    }

    public WeatherRequest(Double latitude, Double longitude) {
        this.latitude = latitude;
        this.longitude = longitude;
    }

    // Getters and Setters
    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public Double getLatitude() {
        return latitude;
    }

    public void setLatitude(Double latitude) {
        this.latitude = latitude;
    }

    public Double getLongitude() {
        return longitude;
    }

    public void setLongitude(Double longitude) {
        this.longitude = longitude;
    }

    public boolean isIncludeForecast() {
        return includeForecast;
    }

    public void setIncludeForecast(boolean includeForecast) {
        this.includeForecast = includeForecast;
    }

    public int getForecastDays() {
        return forecastDays;
    }

    public void setForecastDays(int forecastDays) {
        this.forecastDays = Math.min(Math.max(forecastDays, 1), 14);
    }

    /**
     * 验证请求参数是否有效
     */
    public boolean isValid() {
        return (city != null && !city.trim().isEmpty()) || 
               (latitude != null && longitude != null);
    }

    @Override
    public String toString() {
        return "WeatherRequest{" +
                "city='" + city + '\'' +
                ", latitude=" + latitude +
                ", longitude=" + longitude +
                ", includeForecast=" + includeForecast +
                ", forecastDays=" + forecastDays +
                '}';
    }
}
