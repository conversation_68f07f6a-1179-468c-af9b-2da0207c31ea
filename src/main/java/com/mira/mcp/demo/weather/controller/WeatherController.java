package com.mira.mcp.demo.weather.controller;

import com.mira.mcp.demo.weather.model.dto.WeatherRequest;
import com.mira.mcp.demo.weather.model.dto.WeatherResponse;
import com.mira.mcp.demo.weather.service.WeatherService;
import jakarta.validation.Valid;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

/**
 * 天气查询控制器
 */
@RestController
@RequestMapping("/api/v1/weather")
@Validated
public class WeatherController {
    
    private static final Logger logger = LoggerFactory.getLogger(WeatherController.class);
    
    private final WeatherService weatherService;
    
    public WeatherController(WeatherService weatherService) {
        this.weatherService = weatherService;
    }
    
    /**
     * 通用天气查询接口
     * 
     * @param request 天气查询请求
     * @return 天气响应
     */
    @PostMapping("/query")
    public Mono<ResponseEntity<WeatherResponse>> getWeather(@Valid @RequestBody WeatherRequest request) {
        logger.info("Received weather query request: {}", request);
        
        return weatherService.getWeather(request)
                .map(ResponseEntity::ok)
                .doOnSuccess(response -> logger.info("Weather query completed successfully"))
                .doOnError(error -> logger.error("Weather query failed: {}", error.getMessage()));
    }
    
    /**
     * 根据城市名称获取当前天气
     * 
     * @param city 城市名称
     * @return 当前天气
     */
    @GetMapping("/current/city")
    public Mono<ResponseEntity<WeatherResponse>> getCurrentWeatherByCity(
            @RequestParam @NotBlank @Size(max = 100, message = "城市名称长度不能超过100个字符") String city) {
        logger.info("Getting current weather for city: {}", city);
        
        return weatherService.getCurrentWeatherByCity(city)
                .map(ResponseEntity::ok)
                .doOnSuccess(response -> logger.info("Current weather query for city {} completed", city))
                .doOnError(error -> logger.error("Current weather query for city {} failed: {}", city, error.getMessage()));
    }
    
    /**
     * 根据经纬度获取当前天气
     * 
     * @param latitude 纬度
     * @param longitude 经度
     * @return 当前天气
     */
    @GetMapping("/current/coordinates")
    public Mono<ResponseEntity<WeatherResponse>> getCurrentWeatherByCoordinates(
            @RequestParam @DecimalMin(value = "-90.0", message = "纬度必须在-90到90之间") 
                         @DecimalMax(value = "90.0", message = "纬度必须在-90到90之间") Double latitude,
            @RequestParam @DecimalMin(value = "-180.0", message = "经度必须在-180到180之间") 
                         @DecimalMax(value = "180.0", message = "经度必须在-180到180之间") Double longitude) {
        logger.info("Getting current weather for coordinates: {}, {}", latitude, longitude);
        
        return weatherService.getCurrentWeatherByCoordinates(latitude, longitude)
                .map(ResponseEntity::ok)
                .doOnSuccess(response -> logger.info("Current weather query for coordinates {}, {} completed", latitude, longitude))
                .doOnError(error -> logger.error("Current weather query for coordinates {}, {} failed: {}", latitude, longitude, error.getMessage()));
    }
    
    /**
     * 根据城市名称获取天气预报
     * 
     * @param city 城市名称
     * @param days 预报天数，默认7天，最大14天
     * @return 天气预报
     */
    @GetMapping("/forecast/city")
    public Mono<ResponseEntity<WeatherResponse>> getWeatherForecastByCity(
            @RequestParam @NotBlank @Size(max = 100, message = "城市名称长度不能超过100个字符") String city,
            @RequestParam(defaultValue = "7") @Min(value = 1, message = "预报天数最少1天") 
                                           @Max(value = 14, message = "预报天数最多14天") Integer days) {
        logger.info("Getting weather forecast for city: {}, days: {}", city, days);
        
        return weatherService.getWeatherForecastByCity(city, days)
                .map(ResponseEntity::ok)
                .doOnSuccess(response -> logger.info("Weather forecast query for city {} completed", city))
                .doOnError(error -> logger.error("Weather forecast query for city {} failed: {}", city, error.getMessage()));
    }
    
    /**
     * 根据经纬度获取天气预报
     * 
     * @param latitude 纬度
     * @param longitude 经度
     * @param days 预报天数，默认7天，最大14天
     * @return 天气预报
     */
    @GetMapping("/forecast/coordinates")
    public Mono<ResponseEntity<WeatherResponse>> getWeatherForecastByCoordinates(
            @RequestParam @DecimalMin(value = "-90.0", message = "纬度必须在-90到90之间") 
                         @DecimalMax(value = "90.0", message = "纬度必须在-90到90之间") Double latitude,
            @RequestParam @DecimalMin(value = "-180.0", message = "经度必须在-180到180之间") 
                         @DecimalMax(value = "180.0", message = "经度必须在-180到180之间") Double longitude,
            @RequestParam(defaultValue = "7") @Min(value = 1, message = "预报天数最少1天") 
                                           @Max(value = 14, message = "预报天数最多14天") Integer days) {
        logger.info("Getting weather forecast for coordinates: {}, {}, days: {}", latitude, longitude, days);
        
        return weatherService.getWeatherForecastByCoordinates(latitude, longitude, days)
                .map(ResponseEntity::ok)
                .doOnSuccess(response -> logger.info("Weather forecast query for coordinates {}, {} completed", latitude, longitude))
                .doOnError(error -> logger.error("Weather forecast query for coordinates {}, {} failed: {}", latitude, longitude, error.getMessage()));
    }
    
    /**
     * 健康检查接口
     * 
     * @return 服务状态
     */
    @GetMapping("/health")
    public Mono<ResponseEntity<String>> health() {
        return Mono.just(ResponseEntity.ok("Weather service is running"));
    }
}
