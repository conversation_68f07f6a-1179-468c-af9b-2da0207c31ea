package com.mira.mcp.demo.weather.client.impl;

import com.mira.mcp.demo.weather.client.OpenMeteoClient;
import com.mira.mcp.demo.weather.config.WeatherConfig;
import com.mira.mcp.demo.weather.exception.WeatherServiceException;
import com.mira.mcp.demo.weather.model.external.OpenMeteoResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientException;
import reactor.core.publisher.Mono;

/**
 * OpenMeteo API 客户端实现
 */
@Component
public class OpenMeteoClientImpl implements OpenMeteoClient {
    
    private static final Logger logger = LoggerFactory.getLogger(OpenMeteoClientImpl.class);
    
    private final WebClient webClient;
    private final WeatherConfig weatherConfig;
    
    public OpenMeteoClientImpl(WebClient weatherWebClient, WeatherConfig weatherConfig) {
        this.webClient = weatherWebClient;
        this.weatherConfig = weatherConfig;
    }
    
    @Override
    @Retryable(
        retryFor = {WebClientException.class},
        maxAttemptsExpression = "#{@weatherConfig.retry.maxAttempts}",
        backoff = @Backoff(delayExpression = "#{@weatherConfig.retry.delay}")
    )
    public Mono<OpenMeteoResponse> getCurrentWeather(double latitude, double longitude) {
        logger.debug("Fetching current weather for lat: {}, lon: {}", latitude, longitude);
        
        return webClient.get()
                .uri(uriBuilder -> uriBuilder
                        .path("/forecast")
                        .queryParam("latitude", latitude)
                        .queryParam("longitude", longitude)
                        .queryParam("current", "temperature_2m,relative_humidity_2m,wind_speed_10m,wind_direction_10m,surface_pressure,weather_code")
                        .queryParam("timezone", "auto")
                        .build())
                .retrieve()
                .bodyToMono(OpenMeteoResponse.class)
                .doOnSuccess(response -> logger.debug("Successfully fetched current weather"))
                .doOnError(error -> logger.error("Failed to fetch current weather: {}", error.getMessage()))
                .onErrorMap(WebClientException.class, ex -> 
                    new WeatherServiceException("EXTERNAL_API_ERROR", "获取当前天气失败", ex));
    }
    
    @Override
    @Retryable(
        retryFor = {WebClientException.class},
        maxAttemptsExpression = "#{@weatherConfig.retry.maxAttempts}",
        backoff = @Backoff(delayExpression = "#{@weatherConfig.retry.delay}")
    )
    public Mono<OpenMeteoResponse> getWeatherForecast(double latitude, double longitude, int days) {
        logger.debug("Fetching weather forecast for lat: {}, lon: {}, days: {}", latitude, longitude, days);
        
        return webClient.get()
                .uri(uriBuilder -> uriBuilder
                        .path("/forecast")
                        .queryParam("latitude", latitude)
                        .queryParam("longitude", longitude)
                        .queryParam("daily", "temperature_2m_max,temperature_2m_min,weather_code,precipitation_sum,wind_speed_10m_max")
                        .queryParam("forecast_days", Math.min(Math.max(days, 1), 14))
                        .queryParam("timezone", "auto")
                        .build())
                .retrieve()
                .bodyToMono(OpenMeteoResponse.class)
                .doOnSuccess(response -> logger.debug("Successfully fetched weather forecast"))
                .doOnError(error -> logger.error("Failed to fetch weather forecast: {}", error.getMessage()))
                .onErrorMap(WebClientException.class, ex -> 
                    new WeatherServiceException("EXTERNAL_API_ERROR", "获取天气预报失败", ex));
    }
    
    @Override
    @Retryable(
        retryFor = {WebClientException.class},
        maxAttemptsExpression = "#{@weatherConfig.retry.maxAttempts}",
        backoff = @Backoff(delayExpression = "#{@weatherConfig.retry.delay}")
    )
    public Mono<OpenMeteoResponse> getCurrentWeatherAndForecast(double latitude, double longitude, int days) {
        logger.debug("Fetching current weather and forecast for lat: {}, lon: {}, days: {}", latitude, longitude, days);
        
        return webClient.get()
                .uri(uriBuilder -> uriBuilder
                        .path("/forecast")
                        .queryParam("latitude", latitude)
                        .queryParam("longitude", longitude)
                        .queryParam("current", "temperature_2m,relative_humidity_2m,wind_speed_10m,wind_direction_10m,surface_pressure,weather_code")
                        .queryParam("daily", "temperature_2m_max,temperature_2m_min,weather_code,precipitation_sum,wind_speed_10m_max")
                        .queryParam("forecast_days", Math.min(Math.max(days, 1), 14))
                        .queryParam("timezone", "auto")
                        .build())
                .retrieve()
                .bodyToMono(OpenMeteoResponse.class)
                .doOnSuccess(response -> logger.debug("Successfully fetched current weather and forecast"))
                .doOnError(error -> logger.error("Failed to fetch current weather and forecast: {}", error.getMessage()))
                .onErrorMap(WebClientException.class, ex -> 
                    new WeatherServiceException("EXTERNAL_API_ERROR", "获取天气信息失败", ex));
    }
}
