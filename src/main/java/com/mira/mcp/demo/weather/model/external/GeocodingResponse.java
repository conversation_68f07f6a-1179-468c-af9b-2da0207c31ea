package com.mira.mcp.demo.weather.model.external;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * OpenMeteo Geocoding API 响应模型
 */
public class GeocodingResponse {
    
    private List<Result> results;
    
    @JsonProperty("generationtime_ms")
    private Double generationTimeMs;

    public List<Result> getResults() {
        return results;
    }

    public void setResults(List<Result> results) {
        this.results = results;
    }

    public Double getGenerationTimeMs() {
        return generationTimeMs;
    }

    public void setGenerationTimeMs(Double generationTimeMs) {
        this.generationTimeMs = generationTimeMs;
    }

    /**
     * 地理编码结果
     */
    public static class Result {
        private Long id;
        private String name;
        private Double latitude;
        private Double longitude;
        private Double elevation;
        
        @JsonProperty("feature_code")
        private String featureCode;
        
        @JsonProperty("country_code")
        private String countryCode;
        
        private String country;
        private String timezone;
        private Integer population;
        
        @JsonProperty("country_id")
        private Long countryId;
        
        private String admin1;
        
        @JsonProperty("admin1_id")
        private Long admin1Id;
        
        private String admin2;
        
        @JsonProperty("admin2_id")
        private Long admin2Id;
        
        private String admin3;
        
        @JsonProperty("admin3_id")
        private Long admin3Id;
        
        private String admin4;
        
        @JsonProperty("admin4_id")
        private Long admin4Id;

        // Getters and Setters
        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public Double getLatitude() {
            return latitude;
        }

        public void setLatitude(Double latitude) {
            this.latitude = latitude;
        }

        public Double getLongitude() {
            return longitude;
        }

        public void setLongitude(Double longitude) {
            this.longitude = longitude;
        }

        public Double getElevation() {
            return elevation;
        }

        public void setElevation(Double elevation) {
            this.elevation = elevation;
        }

        public String getFeatureCode() {
            return featureCode;
        }

        public void setFeatureCode(String featureCode) {
            this.featureCode = featureCode;
        }

        public String getCountryCode() {
            return countryCode;
        }

        public void setCountryCode(String countryCode) {
            this.countryCode = countryCode;
        }

        public String getCountry() {
            return country;
        }

        public void setCountry(String country) {
            this.country = country;
        }

        public String getTimezone() {
            return timezone;
        }

        public void setTimezone(String timezone) {
            this.timezone = timezone;
        }

        public Integer getPopulation() {
            return population;
        }

        public void setPopulation(Integer population) {
            this.population = population;
        }

        public Long getCountryId() {
            return countryId;
        }

        public void setCountryId(Long countryId) {
            this.countryId = countryId;
        }

        public String getAdmin1() {
            return admin1;
        }

        public void setAdmin1(String admin1) {
            this.admin1 = admin1;
        }

        public Long getAdmin1Id() {
            return admin1Id;
        }

        public void setAdmin1Id(Long admin1Id) {
            this.admin1Id = admin1Id;
        }

        public String getAdmin2() {
            return admin2;
        }

        public void setAdmin2(String admin2) {
            this.admin2 = admin2;
        }

        public Long getAdmin2Id() {
            return admin2Id;
        }

        public void setAdmin2Id(Long admin2Id) {
            this.admin2Id = admin2Id;
        }

        public String getAdmin3() {
            return admin3;
        }

        public void setAdmin3(String admin3) {
            this.admin3 = admin3;
        }

        public Long getAdmin3Id() {
            return admin3Id;
        }

        public void setAdmin3Id(Long admin3Id) {
            this.admin3Id = admin3Id;
        }

        public String getAdmin4() {
            return admin4;
        }

        public void setAdmin4(String admin4) {
            this.admin4 = admin4;
        }

        public Long getAdmin4Id() {
            return admin4Id;
        }

        public void setAdmin4Id(Long admin4Id) {
            this.admin4Id = admin4Id;
        }
    }
}
