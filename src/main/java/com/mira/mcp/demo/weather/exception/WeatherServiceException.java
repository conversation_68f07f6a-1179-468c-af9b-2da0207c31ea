package com.mira.mcp.demo.weather.exception;

/**
 * 天气服务异常
 */
public class WeatherServiceException extends RuntimeException {
    
    private final String errorCode;
    
    public WeatherServiceException(String message) {
        super(message);
        this.errorCode = "WEATHER_SERVICE_ERROR";
    }
    
    public WeatherServiceException(String message, Throwable cause) {
        super(message, cause);
        this.errorCode = "WEATHER_SERVICE_ERROR";
    }
    
    public WeatherServiceException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }
    
    public WeatherServiceException(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }
    
    public String getErrorCode() {
        return errorCode;
    }
}
