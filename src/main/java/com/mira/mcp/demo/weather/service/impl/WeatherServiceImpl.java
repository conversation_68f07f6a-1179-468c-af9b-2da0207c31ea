package com.mira.mcp.demo.weather.service.impl;

import com.mira.mcp.demo.weather.client.GeocodingClient;
import com.mira.mcp.demo.weather.client.OpenMeteoClient;
import com.mira.mcp.demo.weather.exception.WeatherServiceException;
import com.mira.mcp.demo.weather.model.dto.LocationInfo;
import com.mira.mcp.demo.weather.model.dto.WeatherRequest;
import com.mira.mcp.demo.weather.model.dto.WeatherResponse;
import com.mira.mcp.demo.weather.model.external.OpenMeteoResponse;
import com.mira.mcp.demo.weather.service.WeatherService;
import com.mira.mcp.demo.weather.util.WeatherCodeUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 天气服务实现
 */
@Service
public class WeatherServiceImpl implements WeatherService {
    
    private static final Logger logger = LoggerFactory.getLogger(WeatherServiceImpl.class);
    
    private final OpenMeteoClient openMeteoClient;
    private final GeocodingClient geocodingClient;
    
    public WeatherServiceImpl(OpenMeteoClient openMeteoClient, GeocodingClient geocodingClient) {
        this.openMeteoClient = openMeteoClient;
        this.geocodingClient = geocodingClient;
    }
    
    @Override
    public Mono<WeatherResponse> getWeather(WeatherRequest request) {
        logger.debug("Processing weather request: {}", request);
        
        if (!request.isValid()) {
            return Mono.error(new WeatherServiceException("INVALID_REQUEST", "请提供城市名称或经纬度"));
        }
        
        if (request.getCity() != null && !request.getCity().trim().isEmpty()) {
            return getWeatherByCity(request.getCity().trim(), request.isIncludeForecast(), request.getForecastDays());
        } else {
            return getWeatherByCoordinates(request.getLatitude(), request.getLongitude(), 
                    request.isIncludeForecast(), request.getForecastDays());
        }
    }
    
    @Override
    public Mono<WeatherResponse> getCurrentWeatherByCity(String cityName) {
        return getWeatherByCity(cityName, false, 0);
    }
    
    @Override
    public Mono<WeatherResponse> getCurrentWeatherByCoordinates(double latitude, double longitude) {
        return getWeatherByCoordinates(latitude, longitude, false, 0);
    }
    
    @Override
    public Mono<WeatherResponse> getWeatherForecastByCity(String cityName, int days) {
        return getWeatherByCity(cityName, true, days);
    }
    
    @Override
    public Mono<WeatherResponse> getWeatherForecastByCoordinates(double latitude, double longitude, int days) {
        return getWeatherByCoordinates(latitude, longitude, true, days);
    }
    
    private Mono<WeatherResponse> getWeatherByCity(String cityName, boolean includeForecast, int days) {
        return geocodingClient.getLocationByCity(cityName)
                .flatMap(location -> getWeatherByCoordinates(
                        location.getLatitude(), 
                        location.getLongitude(), 
                        includeForecast, 
                        days)
                        .map(response -> {
                            // 更新位置信息
                            WeatherResponse.LocationInfo locationInfo = new WeatherResponse.LocationInfo();
                            locationInfo.setName(location.getName());
                            locationInfo.setLatitude(location.getLatitude());
                            locationInfo.setLongitude(location.getLongitude());
                            locationInfo.setCountry(location.getCountry());
                            locationInfo.setTimezone(location.getTimezone());
                            response.setLocation(locationInfo);
                            return response;
                        }));
    }
    
    private Mono<WeatherResponse> getWeatherByCoordinates(double latitude, double longitude, 
                                                         boolean includeForecast, int days) {
        Mono<OpenMeteoResponse> weatherMono;
        
        if (includeForecast) {
            weatherMono = openMeteoClient.getCurrentWeatherAndForecast(latitude, longitude, days);
        } else {
            weatherMono = openMeteoClient.getCurrentWeather(latitude, longitude);
        }
        
        return weatherMono.map(openMeteoResponse -> convertToWeatherResponse(openMeteoResponse, includeForecast));
    }
    
    private WeatherResponse convertToWeatherResponse(OpenMeteoResponse openMeteoResponse, boolean includeForecast) {
        WeatherResponse response = new WeatherResponse();
        
        // 设置位置信息
        WeatherResponse.LocationInfo locationInfo = new WeatherResponse.LocationInfo();
        locationInfo.setLatitude(openMeteoResponse.getLatitude());
        locationInfo.setLongitude(openMeteoResponse.getLongitude());
        locationInfo.setTimezone(openMeteoResponse.getTimezone());
        response.setLocation(locationInfo);
        
        // 设置当前天气
        if (openMeteoResponse.getCurrent() != null) {
            WeatherResponse.CurrentWeather currentWeather = new WeatherResponse.CurrentWeather();
            OpenMeteoResponse.Current current = openMeteoResponse.getCurrent();
            
            currentWeather.setTemperature(current.getTemperature2m());
            currentWeather.setHumidity(current.getRelativeHumidity2m());
            currentWeather.setWindSpeed(current.getWindSpeed10m());
            currentWeather.setWindDirection(current.getWindDirection10m());
            currentWeather.setPressure(current.getSurfacePressure());
            currentWeather.setWeatherCode(current.getWeatherCode());
            currentWeather.setWeatherDescription(WeatherCodeUtil.getWeatherDescription(current.getWeatherCode()));
            
            if (current.getTime() != null) {
                currentWeather.setTime(LocalDateTime.parse(current.getTime(), DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            }
            
            response.setCurrent(currentWeather);
        }
        
        // 设置预报信息
        if (includeForecast && openMeteoResponse.getDaily() != null) {
            response.setForecast(convertToForecastList(openMeteoResponse.getDaily()));
        }
        
        return response;
    }

    private List<WeatherResponse.DailyForecast> convertToForecastList(OpenMeteoResponse.Daily daily) {
        List<WeatherResponse.DailyForecast> forecasts = new ArrayList<>();

        if (daily.getTime() == null || daily.getTime().isEmpty()) {
            return forecasts;
        }

        for (int i = 0; i < daily.getTime().size(); i++) {
            WeatherResponse.DailyForecast forecast = new WeatherResponse.DailyForecast();

            // 设置日期
            String dateStr = daily.getTime().get(i);
            forecast.setDate(LocalDateTime.parse(dateStr + "T00:00:00", DateTimeFormatter.ISO_LOCAL_DATE_TIME));

            // 设置温度
            if (daily.getTemperature2mMax() != null && i < daily.getTemperature2mMax().size()) {
                forecast.setMaxTemperature(daily.getTemperature2mMax().get(i));
            }
            if (daily.getTemperature2mMin() != null && i < daily.getTemperature2mMin().size()) {
                forecast.setMinTemperature(daily.getTemperature2mMin().get(i));
            }

            // 设置天气代码和描述
            if (daily.getWeatherCode() != null && i < daily.getWeatherCode().size()) {
                Integer weatherCode = daily.getWeatherCode().get(i);
                forecast.setWeatherCode(weatherCode);
                forecast.setWeatherDescription(WeatherCodeUtil.getWeatherDescription(weatherCode));
            }

            // 设置降水量
            if (daily.getPrecipitationSum() != null && i < daily.getPrecipitationSum().size()) {
                forecast.setPrecipitation(daily.getPrecipitationSum().get(i));
            }

            // 设置风速
            if (daily.getWindSpeed10mMax() != null && i < daily.getWindSpeed10mMax().size()) {
                forecast.setWindSpeed(daily.getWindSpeed10mMax().get(i));
            }

            forecasts.add(forecast);
        }

        return forecasts;
    }
}
