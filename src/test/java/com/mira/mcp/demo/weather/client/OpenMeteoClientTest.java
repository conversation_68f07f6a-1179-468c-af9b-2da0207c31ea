package com.mira.mcp.demo.weather.client;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mira.mcp.demo.weather.client.impl.OpenMeteoClientImpl;
import com.mira.mcp.demo.weather.config.WeatherConfig;
import com.mira.mcp.demo.weather.model.external.OpenMeteoResponse;
import okhttp3.mockwebserver.MockResponse;
import okhttp3.mockwebserver.MockWebServer;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.test.StepVerifier;

import java.io.IOException;

class OpenMeteoClientTest {
    
    private MockWebServer mockWebServer;
    private OpenMeteoClient openMeteoClient;
    private ObjectMapper objectMapper;
    
    @BeforeEach
    void setUp() throws IOException {
        mockWebServer = new MockWebServer();
        mockWebServer.start();
        
        String baseUrl = mockWebServer.url("/").toString();
        WebClient webClient = WebClient.builder().baseUrl(baseUrl).build();
        
        WeatherConfig weatherConfig = new WeatherConfig();
        weatherConfig.setBaseUrl(baseUrl);
        
        openMeteoClient = new OpenMeteoClientImpl(webClient, weatherConfig);
        objectMapper = new ObjectMapper();
    }
    
    @AfterEach
    void tearDown() throws IOException {
        mockWebServer.shutdown();
    }
    
    @Test
    void testGetCurrentWeather_Success() throws Exception {
        // Given
        double latitude = 39.9042;
        double longitude = 116.4074;
        
        OpenMeteoResponse mockResponse = createMockOpenMeteoResponse();
        String responseJson = objectMapper.writeValueAsString(mockResponse);
        
        mockWebServer.enqueue(new MockResponse()
                .setBody(responseJson)
                .addHeader("Content-Type", "application/json"));
        
        // When & Then
        StepVerifier.create(openMeteoClient.getCurrentWeather(latitude, longitude))
                .expectNextMatches(response -> {
                    return response.getLatitude().equals(latitude) &&
                           response.getLongitude().equals(longitude) &&
                           response.getCurrent() != null &&
                           response.getCurrent().getTemperature2m() != null;
                })
                .verifyComplete();
    }
    
    @Test
    void testGetWeatherForecast_Success() throws Exception {
        // Given
        double latitude = 39.9042;
        double longitude = 116.4074;
        int days = 7;
        
        OpenMeteoResponse mockResponse = createMockOpenMeteoResponseWithForecast();
        String responseJson = objectMapper.writeValueAsString(mockResponse);
        
        mockWebServer.enqueue(new MockResponse()
                .setBody(responseJson)
                .addHeader("Content-Type", "application/json"));
        
        // When & Then
        StepVerifier.create(openMeteoClient.getWeatherForecast(latitude, longitude, days))
                .expectNextMatches(response -> {
                    return response.getLatitude().equals(latitude) &&
                           response.getLongitude().equals(longitude) &&
                           response.getDaily() != null &&
                           response.getDaily().getTime() != null &&
                           !response.getDaily().getTime().isEmpty();
                })
                .verifyComplete();
    }
    
    @Test
    void testGetCurrentWeatherAndForecast_Success() throws Exception {
        // Given
        double latitude = 39.9042;
        double longitude = 116.4074;
        int days = 5;
        
        OpenMeteoResponse mockResponse = createMockOpenMeteoResponseWithForecast();
        String responseJson = objectMapper.writeValueAsString(mockResponse);
        
        mockWebServer.enqueue(new MockResponse()
                .setBody(responseJson)
                .addHeader("Content-Type", "application/json"));
        
        // When & Then
        StepVerifier.create(openMeteoClient.getCurrentWeatherAndForecast(latitude, longitude, days))
                .expectNextMatches(response -> {
                    return response.getLatitude().equals(latitude) &&
                           response.getLongitude().equals(longitude) &&
                           response.getCurrent() != null &&
                           response.getDaily() != null;
                })
                .verifyComplete();
    }
    
    @Test
    void testApiError_ShouldPropagateError() {
        // Given
        double latitude = 39.9042;
        double longitude = 116.4074;
        
        mockWebServer.enqueue(new MockResponse()
                .setResponseCode(500)
                .setBody("Internal Server Error"));
        
        // When & Then
        StepVerifier.create(openMeteoClient.getCurrentWeather(latitude, longitude))
                .expectError()
                .verify();
    }
    
    private OpenMeteoResponse createMockOpenMeteoResponse() {
        OpenMeteoResponse response = new OpenMeteoResponse();
        response.setLatitude(39.9042);
        response.setLongitude(116.4074);
        response.setTimezone("Asia/Shanghai");
        response.setGenerationTimeMs(0.5);
        
        OpenMeteoResponse.Current current = new OpenMeteoResponse.Current();
        current.setTime("2024-01-01T12:00");
        current.setTemperature2m(20.5);
        current.setRelativeHumidity2m(65.0);
        current.setWindSpeed10m(5.2);
        current.setWindDirection10m(180);
        current.setSurfacePressure(1013.25);
        current.setWeatherCode(1);
        
        response.setCurrent(current);
        return response;
    }
    
    private OpenMeteoResponse createMockOpenMeteoResponseWithForecast() {
        OpenMeteoResponse response = createMockOpenMeteoResponse();
        
        OpenMeteoResponse.Daily daily = new OpenMeteoResponse.Daily();
        daily.setTime(java.util.List.of("2024-01-01", "2024-01-02", "2024-01-03"));
        daily.setTemperature2mMax(java.util.List.of(25.0, 23.0, 21.0));
        daily.setTemperature2mMin(java.util.List.of(15.0, 13.0, 11.0));
        daily.setWeatherCode(java.util.List.of(1, 2, 3));
        daily.setPrecipitationSum(java.util.List.of(0.0, 2.5, 5.0));
        daily.setWindSpeed10mMax(java.util.List.of(8.0, 10.0, 12.0));
        
        response.setDaily(daily);
        return response;
    }
}
