package com.mira.mcp.demo.weather.service;

import com.mira.mcp.demo.weather.client.GeocodingClient;
import com.mira.mcp.demo.weather.client.OpenMeteoClient;
import com.mira.mcp.demo.weather.exception.WeatherServiceException;
import com.mira.mcp.demo.weather.model.dto.LocationInfo;
import com.mira.mcp.demo.weather.model.dto.WeatherRequest;
import com.mira.mcp.demo.weather.model.dto.WeatherResponse;
import com.mira.mcp.demo.weather.model.external.OpenMeteoResponse;
import com.mira.mcp.demo.weather.service.impl.WeatherServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyDouble;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class WeatherServiceTest {
    
    @Mock
    private OpenMeteoClient openMeteoClient;
    
    @Mock
    private GeocodingClient geocodingClient;
    
    private WeatherService weatherService;
    
    @BeforeEach
    void setUp() {
        weatherService = new WeatherServiceImpl(openMeteoClient, geocodingClient);
    }
    
    @Test
    void testGetCurrentWeatherByCity_Success() {
        // Given
        String cityName = "北京";
        LocationInfo locationInfo = new LocationInfo("北京", 39.9042, 116.4074);
        OpenMeteoResponse openMeteoResponse = createMockOpenMeteoResponse();
        
        when(geocodingClient.getLocationByCity(cityName)).thenReturn(Mono.just(locationInfo));
        when(openMeteoClient.getCurrentWeather(anyDouble(), anyDouble())).thenReturn(Mono.just(openMeteoResponse));
        
        // When & Then
        StepVerifier.create(weatherService.getCurrentWeatherByCity(cityName))
                .expectNextMatches(response -> {
                    return response.getLocation() != null &&
                           response.getLocation().getName().equals("北京") &&
                           response.getCurrent() != null &&
                           response.getCurrent().getTemperature() != null;
                })
                .verifyComplete();
    }
    
    @Test
    void testGetCurrentWeatherByCoordinates_Success() {
        // Given
        double latitude = 39.9042;
        double longitude = 116.4074;
        OpenMeteoResponse openMeteoResponse = createMockOpenMeteoResponse();
        
        when(openMeteoClient.getCurrentWeather(latitude, longitude)).thenReturn(Mono.just(openMeteoResponse));
        
        // When & Then
        StepVerifier.create(weatherService.getCurrentWeatherByCoordinates(latitude, longitude))
                .expectNextMatches(response -> {
                    return response.getLocation() != null &&
                           response.getLocation().getLatitude().equals(latitude) &&
                           response.getLocation().getLongitude().equals(longitude) &&
                           response.getCurrent() != null;
                })
                .verifyComplete();
    }
    
    @Test
    void testGetWeatherWithInvalidRequest_ShouldThrowException() {
        // Given
        WeatherRequest invalidRequest = new WeatherRequest();
        // 没有设置城市名称或经纬度
        
        // When & Then
        StepVerifier.create(weatherService.getWeather(invalidRequest))
                .expectError(WeatherServiceException.class)
                .verify();
    }
    
    @Test
    void testGetWeatherForecastByCity_Success() {
        // Given
        String cityName = "上海";
        int days = 7;
        LocationInfo locationInfo = new LocationInfo("上海", 31.2304, 121.4737);
        OpenMeteoResponse openMeteoResponse = createMockOpenMeteoResponseWithForecast();
        
        when(geocodingClient.getLocationByCity(cityName)).thenReturn(Mono.just(locationInfo));
        when(openMeteoClient.getCurrentWeatherAndForecast(anyDouble(), anyDouble(), anyInt()))
                .thenReturn(Mono.just(openMeteoResponse));
        
        // When & Then
        StepVerifier.create(weatherService.getWeatherForecastByCity(cityName, days))
                .expectNextMatches(response -> {
                    return response.getLocation() != null &&
                           response.getLocation().getName().equals("上海") &&
                           response.getCurrent() != null &&
                           response.getForecast() != null &&
                           !response.getForecast().isEmpty();
                })
                .verifyComplete();
    }
    
    @Test
    void testGeocodingFailure_ShouldPropagateError() {
        // Given
        String cityName = "不存在的城市";
        when(geocodingClient.getLocationByCity(cityName))
                .thenReturn(Mono.error(new WeatherServiceException("CITY_NOT_FOUND", "城市未找到")));
        
        // When & Then
        StepVerifier.create(weatherService.getCurrentWeatherByCity(cityName))
                .expectError(WeatherServiceException.class)
                .verify();
    }
    
    private OpenMeteoResponse createMockOpenMeteoResponse() {
        OpenMeteoResponse response = new OpenMeteoResponse();
        response.setLatitude(39.9042);
        response.setLongitude(116.4074);
        response.setTimezone("Asia/Shanghai");
        
        OpenMeteoResponse.Current current = new OpenMeteoResponse.Current();
        current.setTime("2024-01-01T12:00");
        current.setTemperature2m(20.5);
        current.setRelativeHumidity2m(65.0);
        current.setWindSpeed10m(5.2);
        current.setWindDirection10m(180);
        current.setSurfacePressure(1013.25);
        current.setWeatherCode(1);
        
        response.setCurrent(current);
        return response;
    }
    
    private OpenMeteoResponse createMockOpenMeteoResponseWithForecast() {
        OpenMeteoResponse response = createMockOpenMeteoResponse();
        
        OpenMeteoResponse.Daily daily = new OpenMeteoResponse.Daily();
        daily.setTime(java.util.List.of("2024-01-01", "2024-01-02", "2024-01-03"));
        daily.setTemperature2mMax(java.util.List.of(25.0, 23.0, 21.0));
        daily.setTemperature2mMin(java.util.List.of(15.0, 13.0, 11.0));
        daily.setWeatherCode(java.util.List.of(1, 2, 3));
        daily.setPrecipitationSum(java.util.List.of(0.0, 2.5, 5.0));
        daily.setWindSpeed10mMax(java.util.List.of(8.0, 10.0, 12.0));
        
        response.setDaily(daily);
        return response;
    }
}
