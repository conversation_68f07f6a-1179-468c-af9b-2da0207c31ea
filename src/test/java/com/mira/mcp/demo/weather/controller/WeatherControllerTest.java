package com.mira.mcp.demo.weather.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mira.mcp.demo.weather.model.dto.WeatherRequest;
import com.mira.mcp.demo.weather.model.dto.WeatherResponse;
import com.mira.mcp.demo.weather.service.WeatherService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.reactive.WebFluxTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.reactive.server.WebTestClient;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyDouble;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@WebFluxTest(WeatherController.class)
class WeatherControllerTest {
    
    @Autowired
    private WebTestClient webTestClient;
    
    @MockBean
    private WeatherService weatherService;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    @Test
    void testGetCurrentWeatherByCity_Success() {
        // Given
        String cityName = "北京";
        WeatherResponse mockResponse = createMockWeatherResponse();
        
        when(weatherService.getCurrentWeatherByCity(cityName)).thenReturn(Mono.just(mockResponse));
        
        // When & Then
        webTestClient.get()
                .uri("/api/v1/weather/current/city?city={city}", cityName)
                .accept(MediaType.APPLICATION_JSON)
                .exchange()
                .expectStatus().isOk()
                .expectBody(WeatherResponse.class)
                .value(response -> {
                    assert response.getLocation() != null;
                    assert response.getCurrent() != null;
                });
    }
    
    @Test
    void testGetCurrentWeatherByCoordinates_Success() {
        // Given
        double latitude = 39.9042;
        double longitude = 116.4074;
        WeatherResponse mockResponse = createMockWeatherResponse();
        
        when(weatherService.getCurrentWeatherByCoordinates(latitude, longitude))
                .thenReturn(Mono.just(mockResponse));
        
        // When & Then
        webTestClient.get()
                .uri("/api/v1/weather/current/coordinates?latitude={lat}&longitude={lon}", latitude, longitude)
                .accept(MediaType.APPLICATION_JSON)
                .exchange()
                .expectStatus().isOk()
                .expectBody(WeatherResponse.class)
                .value(response -> {
                    assert response.getLocation() != null;
                    assert response.getCurrent() != null;
                });
    }
    
    @Test
    void testGetWeatherForecastByCity_Success() {
        // Given
        String cityName = "上海";
        int days = 7;
        WeatherResponse mockResponse = createMockWeatherResponseWithForecast();
        
        when(weatherService.getWeatherForecastByCity(cityName, days))
                .thenReturn(Mono.just(mockResponse));
        
        // When & Then
        webTestClient.get()
                .uri("/api/v1/weather/forecast/city?city={city}&days={days}", cityName, days)
                .accept(MediaType.APPLICATION_JSON)
                .exchange()
                .expectStatus().isOk()
                .expectBody(WeatherResponse.class)
                .value(response -> {
                    assert response.getLocation() != null;
                    assert response.getCurrent() != null;
                    assert response.getForecast() != null;
                    assert !response.getForecast().isEmpty();
                });
    }
    
    @Test
    void testPostWeatherQuery_Success() throws Exception {
        // Given
        WeatherRequest request = new WeatherRequest("深圳");
        request.setIncludeForecast(true);
        request.setForecastDays(5);
        
        WeatherResponse mockResponse = createMockWeatherResponseWithForecast();
        
        when(weatherService.getWeather(any(WeatherRequest.class))).thenReturn(Mono.just(mockResponse));
        
        // When & Then
        webTestClient.post()
                .uri("/api/v1/weather/query")
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(request)
                .exchange()
                .expectStatus().isOk()
                .expectBody(WeatherResponse.class)
                .value(response -> {
                    assert response.getLocation() != null;
                    assert response.getCurrent() != null;
                    assert response.getForecast() != null;
                });
    }
    
    @Test
    void testGetCurrentWeatherByCity_InvalidCity_ShouldReturnBadRequest() {
        // When & Then
        webTestClient.get()
                .uri("/api/v1/weather/current/city?city=")
                .accept(MediaType.APPLICATION_JSON)
                .exchange()
                .expectStatus().isBadRequest();
    }
    
    @Test
    void testGetCurrentWeatherByCoordinates_InvalidLatitude_ShouldReturnBadRequest() {
        // When & Then
        webTestClient.get()
                .uri("/api/v1/weather/current/coordinates?latitude=100&longitude=0")
                .accept(MediaType.APPLICATION_JSON)
                .exchange()
                .expectStatus().isBadRequest();
    }
    
    @Test
    void testHealthCheck_Success() {
        // When & Then
        webTestClient.get()
                .uri("/api/v1/weather/health")
                .exchange()
                .expectStatus().isOk()
                .expectBody(String.class)
                .isEqualTo("Weather service is running");
    }
    
    private WeatherResponse createMockWeatherResponse() {
        WeatherResponse response = new WeatherResponse();
        
        WeatherResponse.LocationInfo location = new WeatherResponse.LocationInfo();
        location.setName("北京");
        location.setLatitude(39.9042);
        location.setLongitude(116.4074);
        location.setCountry("中国");
        location.setTimezone("Asia/Shanghai");
        response.setLocation(location);
        
        WeatherResponse.CurrentWeather current = new WeatherResponse.CurrentWeather();
        current.setTemperature(20.5);
        current.setHumidity(65.0);
        current.setWindSpeed(5.2);
        current.setWindDirection(180);
        current.setPressure(1013.25);
        current.setWeatherCode(1);
        current.setWeatherDescription("主要晴朗");
        current.setTime(LocalDateTime.now());
        response.setCurrent(current);
        
        return response;
    }
    
    private WeatherResponse createMockWeatherResponseWithForecast() {
        WeatherResponse response = createMockWeatherResponse();
        
        WeatherResponse.DailyForecast forecast1 = new WeatherResponse.DailyForecast();
        forecast1.setDate(LocalDateTime.now().plusDays(1));
        forecast1.setMaxTemperature(25.0);
        forecast1.setMinTemperature(15.0);
        forecast1.setWeatherCode(1);
        forecast1.setWeatherDescription("主要晴朗");
        forecast1.setPrecipitation(0.0);
        forecast1.setWindSpeed(8.0);
        
        WeatherResponse.DailyForecast forecast2 = new WeatherResponse.DailyForecast();
        forecast2.setDate(LocalDateTime.now().plusDays(2));
        forecast2.setMaxTemperature(23.0);
        forecast2.setMinTemperature(13.0);
        forecast2.setWeatherCode(2);
        forecast2.setWeatherDescription("部分多云");
        forecast2.setPrecipitation(2.5);
        forecast2.setWindSpeed(10.0);
        
        response.setForecast(java.util.List.of(forecast1, forecast2));
        
        return response;
    }
}
