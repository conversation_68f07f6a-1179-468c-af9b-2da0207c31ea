package com.mira.mcp.demo.weather.integration;

import com.mira.mcp.demo.weather.model.dto.WeatherRequest;
import com.mira.mcp.demo.weather.model.dto.WeatherResponse;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.reactive.server.WebTestClient;

/**
 * 天气服务集成测试
 * 注意：这些测试需要网络连接到 OpenMeteo API
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("test")
class WeatherIntegrationTest {
    
    @Autowired
    private WebTestClient webTestClient;
    
    @Test
    void testGetCurrentWeatherByCity_RealAPI() {
        // Given
        String cityName = "Beijing";
        
        // When & Then
        webTestClient.get()
                .uri("/api/v1/weather/current/city?city={city}", cityName)
                .accept(MediaType.APPLICATION_JSON)
                .exchange()
                .expectStatus().isOk()
                .expectBody(WeatherResponse.class)
                .value(response -> {
                    assert response.getLocation() != null;
                    assert response.getLocation().getName() != null;
                    assert response.getLocation().getLatitude() != null;
                    assert response.getLocation().getLongitude() != null;
                    assert response.getCurrent() != null;
                    assert response.getCurrent().getTemperature() != null;
                    assert response.getQueryTime() != null;
                });
    }
    
    @Test
    void testGetCurrentWeatherByCoordinates_RealAPI() {
        // Given - Beijing coordinates
        double latitude = 39.9042;
        double longitude = 116.4074;
        
        // When & Then
        webTestClient.get()
                .uri("/api/v1/weather/current/coordinates?latitude={lat}&longitude={lon}", latitude, longitude)
                .accept(MediaType.APPLICATION_JSON)
                .exchange()
                .expectStatus().isOk()
                .expectBody(WeatherResponse.class)
                .value(response -> {
                    assert response.getLocation() != null;
                    assert response.getLocation().getLatitude().equals(latitude);
                    assert response.getLocation().getLongitude().equals(longitude);
                    assert response.getCurrent() != null;
                    assert response.getCurrent().getTemperature() != null;
                    assert response.getCurrent().getWeatherDescription() != null;
                });
    }
    
    @Test
    void testGetWeatherForecastByCity_RealAPI() {
        // Given
        String cityName = "Shanghai";
        int days = 5;
        
        // When & Then
        webTestClient.get()
                .uri("/api/v1/weather/forecast/city?city={city}&days={days}", cityName, days)
                .accept(MediaType.APPLICATION_JSON)
                .exchange()
                .expectStatus().isOk()
                .expectBody(WeatherResponse.class)
                .value(response -> {
                    assert response.getLocation() != null;
                    assert response.getLocation().getName() != null;
                    assert response.getCurrent() != null;
                    assert response.getForecast() != null;
                    assert response.getForecast().size() <= days;
                    
                    // 验证预报数据
                    response.getForecast().forEach(forecast -> {
                        assert forecast.getDate() != null;
                        assert forecast.getMaxTemperature() != null;
                        assert forecast.getMinTemperature() != null;
                        assert forecast.getWeatherDescription() != null;
                    });
                });
    }
    
    @Test
    void testPostWeatherQuery_WithCity_RealAPI() {
        // Given
        WeatherRequest request = new WeatherRequest("Guangzhou");
        request.setIncludeForecast(true);
        request.setForecastDays(3);
        
        // When & Then
        webTestClient.post()
                .uri("/api/v1/weather/query")
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(request)
                .exchange()
                .expectStatus().isOk()
                .expectBody(WeatherResponse.class)
                .value(response -> {
                    assert response.getLocation() != null;
                    assert response.getCurrent() != null;
                    assert response.getForecast() != null;
                    assert response.getForecast().size() <= 3;
                });
    }
    
    @Test
    void testPostWeatherQuery_WithCoordinates_RealAPI() {
        // Given - Shenzhen coordinates
        WeatherRequest request = new WeatherRequest(22.5431, 114.0579);
        request.setIncludeForecast(false);
        
        // When & Then
        webTestClient.post()
                .uri("/api/v1/weather/query")
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(request)
                .exchange()
                .expectStatus().isOk()
                .expectBody(WeatherResponse.class)
                .value(response -> {
                    assert response.getLocation() != null;
                    assert response.getLocation().getLatitude().equals(22.5431);
                    assert response.getLocation().getLongitude().equals(114.0579);
                    assert response.getCurrent() != null;
                    assert response.getForecast() == null; // 没有请求预报
                });
    }
    
    @Test
    void testInvalidCity_ShouldReturnError() {
        // Given
        String invalidCity = "不存在的城市名称12345";
        
        // When & Then
        webTestClient.get()
                .uri("/api/v1/weather/current/city?city={city}", invalidCity)
                .accept(MediaType.APPLICATION_JSON)
                .exchange()
                .expectStatus().isBadRequest()
                .expectBody()
                .jsonPath("$.error").exists()
                .jsonPath("$.message").exists()
                .jsonPath("$.timestamp").exists();
    }
    
    @Test
    void testInvalidCoordinates_ShouldReturnValidationError() {
        // Given - Invalid latitude (> 90)
        double invalidLatitude = 100.0;
        double longitude = 0.0;
        
        // When & Then
        webTestClient.get()
                .uri("/api/v1/weather/current/coordinates?latitude={lat}&longitude={lon}", invalidLatitude, longitude)
                .accept(MediaType.APPLICATION_JSON)
                .exchange()
                .expectStatus().isBadRequest();
    }
    
    @Test
    void testHealthEndpoint() {
        // When & Then
        webTestClient.get()
                .uri("/api/v1/weather/health")
                .exchange()
                .expectStatus().isOk()
                .expectBody(String.class)
                .isEqualTo("Weather service is running");
    }
}
