# Test Configuration
spring.application.name=mcp-demo-test
server.port=0

# Weather Service Configuration for Testing
weather.openmeteo.base-url=https://api.open-meteo.com/v1
weather.openmeteo.timeout=5000
weather.openmeteo.retry.max-attempts=2
weather.openmeteo.retry.delay=500

# Logging Configuration for Testing
logging.level.com.mira.mcp.demo.weather=DEBUG
logging.level.org.springframework.web.reactive.function.client=WARN
logging.level.reactor.netty.http.client=WARN

# Actuator Configuration for Testing
management.endpoints.web.exposure.include=health,info
management.endpoint.health.show-details=always
